import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';
import { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';
import { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },
  
  {
    path: 'dashboard',
    canActivate: [AuthGuard],
    component: LaunchpadHomeComponent,
  },
  {
    path: 'agent-list',
    canActivate: [AuthGuard],
    component: AgentsFilterComponent,
  },
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: '/login',
  },
];
