::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

.custom-header-container {
  width: 100%;
  box-sizing: border-box;
  margin: 72px;
}

.header-flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 52px 52px 0 52px;
}

.header-left-logo {
  display: flex;
  align-items: center;
  width: 160px;
  height: 36px;
}

.axos-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.header-right-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.custom-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);
}

.header-right-content>* {
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}


// Profile dropdown styles (matching navbar dropdown)
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 15; // Reduced from 1000 to prevent hiding content below
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.profile-dropdown-content {
  display: flex;
  flex-direction: column;
}

.profile-info {
  padding: 12px;
  border-radius: 6px;
}

.profile-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--dropdown-text);
  margin-bottom: 4px;
}

.profile-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.profile-divider {
  height: 1px;
  background-color: var(--border-color, #e5e7eb);
  margin: 8px 0;
}

.profile-actions {
  display: flex;
  flex-direction: column;
}

.profile-action-item {
  color: var(--nav-text, #666d99);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);

    &:after {
      opacity: 0.1;
    }
  }

  // Add the gradient overlay effect
  &:after {
    content: "";
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .action-label {
    font-weight: 500;
    font-size: 1rem;
    color: var(--dropdown-text);
  }
}