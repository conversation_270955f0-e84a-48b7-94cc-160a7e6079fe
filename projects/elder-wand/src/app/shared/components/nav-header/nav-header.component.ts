import {
  Component,
  OnInit,
  HostListener,
  Output,
  EventEmitter,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent, HeadingComponent } from '@awe/play-comp-library';
import { ThemeService } from '../../services/theme.service';
import { ButtonComponent, IconComponent } from "@ava/play-comp-library";
import { AuthService, TokenStorageService } from '@shared/index';
import { Router } from '@angular/router';
import { ToastService } from 'projects/experience-studio/src/app/shared/services/toast.service';
import { createLogger } from 'projects/experience-studio/src/app/shared/utils';

interface User {
  name: string;
  role: string;
  type: string;
  projects?: string[];
}

interface Project {
  id: string;
  name: string;
  logo: string;
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [CommonModule, HeaderComponent, IconComponent, HeadingComponent, ButtonComponent],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
})
export class NavHeaderComponent implements OnInit {
  logoSrc: string = '';
  themeToggleIcon: string = '';
  consoleIcon: string = '';
  userAvatar: string = '';
  translateIcon: string = '';
  isDropdownOpen: boolean = false;
  currentProject: string = '';
  selectedUser: User | null = null;
  isFlipping: boolean = false;
  isDarkTheme: boolean = false;
  showProfileMenu: boolean = false;
  @Input() userImage?: string;
  private logger = createLogger('NavHeaderComponent');
  public redirectUrl = '';
  profileDropdownOpen: boolean = false;

  userName: string = '';
  userEmail: string = '';
  


  @Input() projects: Project[] = [
    { id: 'axos', name: 'AXOS', logo: 'axos-client-logo.svg' },
    { id: 'cvs', name: 'CVS', logo: 'cvs-client-logo.svg' },
  ];

  @Output() userSelected = new EventEmitter<User>();
  @Output() projectSwitched = new EventEmitter<string>();

  users: User[] = [
    // {
    //   name: 'John',
    //   role: 'Sales',
    //   type: 'Sales',
    // },
    {
      name: 'Akshay',
      role: 'Project Team',
      type: 'Project Team',
      projects: ['axos', 'cvs'],
    },
  ];
  constructor(private themeService: ThemeService,
    private authService: AuthService,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private toastService: ToastService,
  ) { }

  ngOnInit(): void {
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;
    this.logoSrc = `ascendion-logo-light.svg`;
    this.themeToggleIcon = `svgs/header/toggle-theme/theme-toggle-light.svg`;
    this.consoleIcon = `svgs/header/menu-light.svg`;
    this.userAvatar = `svgs/header/user-avatar.svg`;
    this.translateIcon = `svgs/header/translate.svg`;

    if (this.projects.length > 0) {
      this.currentProject = this.projects[0].name;
      this.startLogoFlip();
    }
    this.updateThemeAssets();
    new MutationObserver(() => this.updateThemeAssets()).observe(
      document.body,
      {
        attributes: true,
        attributeFilter: ['class'],
      },
    );
    this.selectUser(this.users[0])
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    // this.themeToggleIcon = `assets/svgs/header/toggle-theme/theme-toggle-${currentTheme}.svg`;
    // this.consoleIcon = `assets/svgs/header/menu-${currentTheme}.svg`;
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  // getDisplayName(): string {
  //   return this.tokenStorageService.getDaName() || 'User';
  // }
  // getEmail(): string {
  //   return this.tokenStorageService.getDaUsername() || '<EMAIL>';
  // }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }

  // Handle logout
  onLogout() {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        },
      });
    }
  }
  onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).subscribe({
        next: () => this.logger.debug('Login successful'),
        error: (error) => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      this.logger.error('Error during login:', error);
      this.toastService.error('Login failed');
    }
  }

   // Load user data from cookies
  loadUserData(): void {
    this.userName = this.tokenStorageService.getDaName() || 'User';
    this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';
  }



  getInitials(name: string): string {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
    return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();
  }

 
  // Toggle profile dropdown
  toggleProfileDropdown(): void {
    this.profileDropdownOpen = !this.profileDropdownOpen;
    // Close nav dropdowns when profile dropdown opens
    // if (this.profileDropdownOpen) {
    //   this.closeAllDropdowns();
    // }
  }

  selectUser(user: User): void {
    this.selectedUser = user;
    this.userSelected.emit(user);
    this.isDropdownOpen = false;
  }

  switchProject(project: string): void {
    if (this.currentProject !== project) {
      this.currentProject = project;
      this.projectSwitched.emit(project);
      this.startLogoFlip();
    }
  }

  getAvailableProjects(user: User): Project[] {
    if (user.type === 'Sales') return [];
    return this.projects.filter((p) => user.projects?.includes(p.id));
  }

  getCurrentProjectLogo(): string {
    const project = this.projects.find((p) => p.name === this.currentProject);
    return project ? project.logo : '';
  }

  startLogoFlip(): void {
    if (this.currentProject) {
      this.isFlipping = true;
    } else {
      this.isFlipping = false;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.profile-dropdown')) {
      this.isDropdownOpen = false;
    }
  }
}
