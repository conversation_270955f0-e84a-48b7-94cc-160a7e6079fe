<awe-header theme="light" class="custom-header-container">
  <div left-content class="header-left-logo">
    <img src="assets/icons/axos.jpg" class="axos-logo" alt="Axos Logo" />
  </div>
  <div right-content class="header-right-content">
    <ava-icon
      iconName="languages"
      iconColor="#666D99"
      iconSize="24px"
    ></ava-icon>
    <ava-icon iconName="sun" iconColor="#666D99" iconSize="24px"></ava-icon>
  </div>
    <div class="profile-container header-right-content">
      <div
        class="cursor-pointer d-flex justify-content-center align-items-center"
        [class.active]="profileDropdownOpen"
        (click)="toggleProfileDropdown()"
      >
        <img  src="assets/icons/user-avatar.svg" alt="User Profile" />
      </div>

      <!-- Profile Dropdown -->
      <div class="profile-dropdown" [class.visible]="profileDropdownOpen">
        <div class="profile-dropdown-content">
          <div class="profile-info">
            <div class="profile-name">{{ userName }}</div>
            <div class="profile-email" *ngIf="userEmail">{{ userEmail }}</div>
          </div>
          <div class="profile-divider"></div>
          <div class="profile-actions">
            <div class="profile-action-item" (click)="onLogout()">
              <span class="action-label">Logout</span>
            </div>
          </div>
        </div>
      </div>
    </div>
</awe-header>
