// Environment configuration for <PERSON> Wand (Launchpad) Application
export const environment = {
    production: false,
  
  // Application URLs
    experienceStudioUrl: 'http://localhost:4201',
    productStudioUrl: 'http://localhost:4202',

    elderWandApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
    elderWandRedirectUrl: 'http://localhost:4200/launchpad/',

  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to default
  apiBaseUrl: (window as any).__env?.API_BASE_URL || 'http://localhost:3000',
  apiUrl: (window as any).__env?.API_BASE_URL || 'http://localhost:3000',
  
  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl = (window as any).__env?.API_BASE_URL || 'http://localhost:3000';
    return `${baseUrl}${endpoint}`;
  }
  };