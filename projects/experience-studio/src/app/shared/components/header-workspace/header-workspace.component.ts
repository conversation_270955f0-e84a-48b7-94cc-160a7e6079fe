import { Component, ChangeDetectionStrategy, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { WorkspaceStateService } from '../../services/workspace-state.service';
import { GlobalSearchComponent } from '../global-search/global-search.component';
import { ThemeToggleComponent } from '../theme-toggle/theme-toggle.component';

/**
 * Header Workspace Component - Enhanced header for MLO Workspace
 * Provides navigation, global search, and user controls
 * 
 * Features:
 * - Workspace/Showcase navigation
 * - Global search integration
 * - Theme toggle
 * - User menu and notifications
 * - Responsive design
 */
@Component({
  selector: 'app-header-workspace',
  standalone: true,
  imports: [
    CommonModule,
    GlobalSearchComponent,
    ThemeToggleComponent
  ],
  template: `
    <header class="workspace-header" role="banner">
      <div class="header-content">
        <!-- Logo and Navigation -->
        <div class="header-left">
          <div class="logo-section">
            <button 
              class="logo-btn"
              (click)="navigateToHome()"
              aria-label="MLO Workspace Home">
              <div class="logo">
                <span class="logo-text">MLO</span>
                <span class="logo-subtitle">Workspace</span>
              </div>
            </button>
          </div>
          
          <nav class="main-nav" role="navigation" aria-label="Main navigation">
            <button 
              class="nav-item"
              [class.active]="currentView() === 'dashboard'"
              (click)="navigateTo('dashboard')"
              [attr.aria-current]="currentView() === 'dashboard' ? 'page' : null">
              <i class="fas fa-th-large" aria-hidden="true"></i>
              <span>Workspace</span>
            </button>
            <button 
              class="nav-item"
              [class.active]="currentView() === 'showcase'"
              (click)="navigateTo('showcase')"
              [attr.aria-current]="currentView() === 'showcase' ? 'page' : null">
              <i class="fas fa-star" aria-hidden="true"></i>
              <span>Showcase</span>
            </button>
          </nav>
        </div>
        
        <!-- Global Search -->
        <div class="header-center">
          <app-global-search
            [query]="searchQuery()"
            (search)="onSearch($event)"
            (focus)="onSearchFocus()"
            placeholder="Search projects, templates, and community...">
          </app-global-search>
        </div>
        
        <!-- User Actions -->
        <div class="header-right">
          <!-- Theme Toggle -->
          <app-theme-toggle
            [theme]="theme()"
            (themeChange)="onThemeChange($event)">
          </app-theme-toggle>
          
          <!-- Notifications -->
          <button 
            class="header-action notifications-btn"
            (click)="toggleNotifications()"
            [class.has-notifications]="notificationCount() > 0"
            [attr.aria-label]="'Notifications' + (notificationCount() > 0 ? ' (' + notificationCount() + ' unread)' : '')">
            <i class="fas fa-bell" aria-hidden="true"></i>
            @if (notificationCount() > 0) {
              <span class="notification-badge" aria-hidden="true">{{ notificationCount() }}</span>
            }
          </button>
          
          <!-- User Menu -->
          <div class="user-menu-container">
            <button 
              class="header-action user-menu-btn"
              (click)="toggleUserMenu()"
              [attr.aria-expanded]="showUserMenu()"
              aria-haspopup="true"
              aria-label="User menu">
              <div class="user-avatar">
                <img 
                  [src]="currentUser()?.avatar || '/assets/images/default-avatar.png'"
                  [alt]="currentUser()?.name || 'User avatar'"
                  class="avatar-image">
              </div>
            </button>
            
            @if (showUserMenu()) {
              <div class="user-menu-dropdown" role="menu">
                <div class="user-info">
                  <div class="user-details">
                    <span class="user-name">{{ currentUser()?.name || 'User' }}</span>
                    <span class="user-email">{{ currentUser()?.email || '<EMAIL>' }}</span>
                  </div>
                </div>
                
                <div class="menu-divider"></div>
                
                <button class="menu-item" role="menuitem" (click)="onMenuAction('profile')">
                  <i class="fas fa-user" aria-hidden="true"></i>
                  <span>Profile</span>
                </button>
                
                <button class="menu-item" role="menuitem" (click)="onMenuAction('settings')">
                  <i class="fas fa-cog" aria-hidden="true"></i>
                  <span>Settings</span>
                </button>
                
                <button class="menu-item" role="menuitem" (click)="onMenuAction('help')">
                  <i class="fas fa-question-circle" aria-hidden="true"></i>
                  <span>Help & Support</span>
                </button>
                
                <div class="menu-divider"></div>
                
                <button class="menu-item logout" role="menuitem" (click)="onMenuAction('logout')">
                  <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                  <span>Sign Out</span>
                </button>
              </div>
            }
          </div>
        </div>
      </div>
      
      <!-- Mobile Navigation -->
      <div class="mobile-nav" [class.visible]="showMobileNav()">
        <button 
          class="mobile-nav-item"
          [class.active]="currentView() === 'dashboard'"
          (click)="navigateTo('dashboard')">
          <i class="fas fa-th-large" aria-hidden="true"></i>
          <span>Workspace</span>
        </button>
        <button 
          class="mobile-nav-item"
          [class.active]="currentView() === 'showcase'"
          (click)="navigateTo('showcase')">
          <i class="fas fa-star" aria-hidden="true"></i>
          <span>Showcase</span>
        </button>
      </div>
    </header>
  `,
  styleUrls: ['./header-workspace.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HeaderWorkspaceComponent {
  private readonly router = inject(Router);
  private readonly workspaceState = inject(WorkspaceStateService);

  // Component state
  private readonly _showUserMenu = signal<boolean>(false);
  private readonly _showNotifications = signal<boolean>(false);
  private readonly _showMobileNav = signal<boolean>(false);
  private readonly _notificationCount = signal<number>(3); // Mock data
  private readonly _currentUser = signal<{name: string, email: string, avatar: string} | null>({
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '/assets/images/default-avatar.png'
  });

  // Public readonly signals from workspace state
  readonly currentView = this.workspaceState.currentView;
  readonly searchQuery = this.workspaceState.searchQuery;
  readonly theme = this.workspaceState.theme;

  // Component signals
  readonly showUserMenu = this._showUserMenu.asReadonly();
  readonly showNotifications = this._showNotifications.asReadonly();
  readonly showMobileNav = this._showMobileNav.asReadonly();
  readonly notificationCount = this._notificationCount.asReadonly();
  readonly currentUser = this._currentUser.asReadonly();

  /**
   * Navigate to home/dashboard
   */
  navigateToHome(): void {
    this.router.navigate(['/experience/main']);
  }

  /**
   * Navigate to specific workspace view
   */
  navigateTo(view: 'dashboard' | 'showcase'): void {
    const route = view === 'dashboard' ? '/experience/main' : '/experience/showcase';
    this.router.navigate([route]);
    this._showMobileNav.set(false);
  }

  /**
   * Handle search input
   */
  onSearch(query: string): void {
    this.workspaceState.setSearchQuery(query);

    // If we're on the showcase page, trigger a search
    if (this.currentView() === 'showcase') {
      // The showcase page component will listen to search query changes
      // and trigger the appropriate API calls
    }
  }

  /**
   * Handle search focus
   */
  onSearchFocus(): void {
    // Could trigger search suggestions or analytics
  }

  /**
   * Handle theme change
   */
  onThemeChange(theme: 'light' | 'dark' | 'system'): void {
    this.workspaceState.setTheme(theme);
  }

  /**
   * Toggle notifications panel
   */
  toggleNotifications(): void {
    this._showNotifications.set(!this._showNotifications());
    this._showUserMenu.set(false); // Close other menus
  }

  /**
   * Toggle user menu
   */
  toggleUserMenu(): void {
    this._showUserMenu.set(!this._showUserMenu());
    this._showNotifications.set(false); // Close other menus
  }

  /**
   * Toggle mobile navigation
   */
  toggleMobileNav(): void {
    this._showMobileNav.set(!this._showMobileNav());
  }

  /**
   * Handle user menu actions
   */
  onMenuAction(action: string): void {
    this._showUserMenu.set(false);
    
    switch (action) {
      case 'profile':
        // Navigate to profile
        break;
      case 'settings':
        // Navigate to settings
        break;
      case 'help':
        // Navigate to help
        break;
      case 'logout':
        // Handle logout
        break;
    }
  }
}
