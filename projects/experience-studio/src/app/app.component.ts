import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { environment } from '../environments/environment';
import { NavHeaderComponent } from "./shared/components/nav-header/nav-header.component";

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    NavHeaderComponent
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
userProfile: any;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    public authService: AuthService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.experianceApiAuthUrl,
      redirectUrl: environment.experianceRedirectUrl ,
      postLoginRedirectUrl: '/',
      appName: 'experience-studio',
    };
    this.authService.setAuthConfig(authConfig);
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    
    if (!this.tokenStorage.getCookie('org_path')) {
      this.tokenStorage.setCookie(
        'org_path',
        'ascendion@core@genai@aava::201@202@203@204',
      );
    }
    
    this.router.events.subscribe((event) => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
      } else {
        this.showHeaderAndNav = true;
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }
}
