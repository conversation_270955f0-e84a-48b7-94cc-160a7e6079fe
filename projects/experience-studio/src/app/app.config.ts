import { provideHttpClient, withInterceptors, HTTP_INTERCEPTORS, withInterceptorsFromDi } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection, SecurityContext } from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';
import { provideMarkdown, MARKED_OPTIONS, MarkdownModule } from 'ngx-markdown';
import { provideAnimations } from '@angular/platform-browser/animations';
import { LOGGER_CONFIG, LoggerService } from './shared/utils/logger';
import { environment } from '../environments/environment';
import { CacheInterceptor } from './shared/interceptors/cache.interceptor';
import { HttpErrorInterceptor } from './shared/interceptors/http-error.interceptor';
import { LongRequestInterceptor } from './shared/interceptors/long-request.interceptor';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import { SelectivePreloadingStrategy } from './shared/strategies/selective-preloading-strategy';

import { routes } from './app.routes';
import { LucideAngularModule, Hammer, User, Settings, Info, ChevronLeft, ChevronRight, ShieldAlert, Hourglass, CircleCheck, XCircle, AlignVerticalDistributeStart, CircleCheckBig, MoveLeft, Play, CalendarDays, EllipsisVertical, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Edit, Trash, Plus, Minus, ChevronDown, ChevronUp, Eye, Home, Layout, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip, Bot, Archive, Copy, Trash2, Users, Wrench, TrendingUp, PanelLeft, BookOpen, NotebookText, Redo, RotateCcw, Swords, Undo, Pencil, RotateCw, SendHorizontal, WandSparkles, MousePointer2, Hand, ZoomIn, ZoomOut, Clock, CircleX, FileText, Download, Save } from 'lucide-angular';

export const appConfig: ApplicationConfig = {
  providers: [
    // Configure HTTP client with interceptors
    provideHttpClient(
      withInterceptors([LongRequestInterceptor, HttpErrorInterceptor, CacheInterceptor])
    ),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      routes,
      withPreloading(SelectivePreloadingStrategy)
    ),
     importProvidersFrom(
          LucideAngularModule.pick({
            Hammer,
            User,
            Settings,
            Info,
            ChevronLeft,
            ChevronRight,
            ShieldAlert,
            Hourglass,
            CircleCheck,
            XCircle,
            AlignVerticalDistributeStart,
            CircleCheckBig,
            MoveLeft,
            Play,
            CalendarDays,
            EllipsisVertical,
            SquarePen,
            Wifi,
            Search,
            AlertCircle,
            EyeOff,
            Mail,
            Phone,
            Check,
            X,
            Edit,
            Trash,
            Plus,
            Minus,
            ChevronDown,
            ChevronUp,
            Eye,
            Home,
            Layout,
            Bell,
            Grid,
            Star,
            Leaf,
            CheckCircle,
            AlertTriangle,
            XOctagon,
            Sparkles,
            Slash,
            Feather,
            Globe,
            Send,
            Box,
            Paperclip,
            Bot,
            Archive,
            Copy,
            Trash2,
            Users,
            Wrench,
            TrendingUp,
            PanelLeft,
            BookOpen,
            NotebookText,
            Redo,
            RotateCcw,
            Swords,
            Undo,
            Pencil,
            RotateCw,
            SendHorizontal,
            WandSparkles,
            MousePointer2,
            Hand,
            ZoomIn,
            ZoomOut,
            Clock,
            CircleX,
            FileText,
            Download,
            Save
          }),
          MarkdownModule.forRoot(),
        ),
    SelectivePreloadingStrategy,
    provideMarkdown({
      sanitize: SecurityContext.HTML,
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true,
          pedantic: false
        }
      }
    }),
    provideAnimations(),
    {
      provide: LOGGER_CONFIG,
      useValue: {
        level: environment.production ? 'warn' : 'debug',
        enableConsoleOutput: !environment.production
      }
    },
    LoggerService
  ],

};
