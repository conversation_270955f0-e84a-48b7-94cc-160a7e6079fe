import { provideHttpClient, withInterceptors, HTTP_INTERCEPTORS, withInterceptorsFromDi } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection, SecurityContext } from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';
import { provideMarkdown, MARKED_OPTIONS } from 'ngx-markdown';
import { provideAnimations } from '@angular/platform-browser/animations';
import { LOGGER_CONFIG, LoggerService } from './shared/utils/logger';
import { environment } from '../environments/environment';
import { CacheInterceptor } from './shared/interceptors/cache.interceptor';
import { HttpErrorInterceptor } from './shared/interceptors/http-error.interceptor';
import { LongRequestInterceptor } from './shared/interceptors/long-request.interceptor';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import { SelectivePreloadingStrategy } from './shared/strategies/selective-preloading-strategy';

import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    // Configure HTTP client with interceptors
    provideHttpClient(
      withInterceptors([LongRequestInterceptor, HttpErrorInterceptor, CacheInterceptor])
    ),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      routes,
      withPreloading(SelectivePreloadingStrategy)
    ),
    SelectivePreloadingStrategy,
    provideMarkdown({
      sanitize: SecurityContext.HTML,
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true,
          pedantic: false
        }
      }
    }),
    provideAnimations(),
    {
      provide: LOGGER_CONFIG,
      useValue: {
        level: environment.production ? 'warn' : 'debug',
        enableConsoleOutput: !environment.production
      }
    },
    LoggerService
  ],
};
