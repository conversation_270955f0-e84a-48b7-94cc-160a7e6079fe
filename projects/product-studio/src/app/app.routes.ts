import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },
  
  {
    path: 'dashboard',
    canActivate: [AuthGuard],
    loadChildren: () => import('./product/product-studio-routes').then(m => m.PRODUCT_STUDIO_ROUTES),
  },
  {
    path: '',
    canActivate: [AuthGuard],
    loadChildren: () => import('./product/product-studio-routes').then(m => m.PRODUCT_STUDIO_ROUTES),
  },
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: '/login',
  }
];
