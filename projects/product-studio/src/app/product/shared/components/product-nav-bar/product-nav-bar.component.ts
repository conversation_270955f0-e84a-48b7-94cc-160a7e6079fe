import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';

import { ChangeDetectorRef, Component, Input, OnInit, Output } from '@angular/core';
import {
  ButtonComponent,
  HeaderComponent,
  HeadingComponent,
} from '@awe/play-comp-library';
import { CommonModule } from '@angular/common';
import { UserProfile } from '../../models/user-profile.model';
import { ThemeServiceService } from '../../services/auth-config-service/theme-service.service';
import { AppConstants } from '../../appConstants';
import { AuthService, TokenStorageService } from '@shared/index';
import { ToastService } from 'projects/experience-studio/src/app/shared/services/toast.service';
import { Router } from '@angular/router';
import { createLogger } from 'projects/experience-studio/src/app/shared/utils/logger';
// import { createLogger } from 'projects/experience-studio/src/app/shared/utils';
// import { ThemeServiceService } from '../../services/theme-service.service';

@Component({
  selector: 'app-product-nav-bar',
  standalone: true,
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  templateUrl: './product-nav-bar.component.html',
  styleUrl: './product-nav-bar.component.scss',
})
export class ProductNavBarComponent implements OnInit {
  // @Output() logout = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';
private logger = createLogger('NavHeaderComponent');
  constructor(
    private themeService: ThemeServiceService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private toastService: ToastService,
  ) {}

  public redirectUrl = '';

  ngOnInit(): void {
    this.updateThemeAssets();
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;
    new MutationObserver(() => this.updateThemeAssets()).observe(
      document.body,
      {
        attributes: true,
        attributeFilter: ['class'],
      },
    );
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }

   

  // Handle logout
  onLogout() {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        },
      });
    }
  }
  onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).subscribe({
        next: () => this.logger.debug('Login successful'),
        error: (error) => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      this.logger.error('Error during login:', error);
      this.toastService.error('Login failed');
    }
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `svgs/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.userProfile?.displayName || 'User Profile';
  }

  getEmail(): string {
    return (
      this.userProfile?.mail ||
      this.userProfile?.userPrincipalName ||
      '<EMAIL>'
    );
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `svgs/product-studio-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `svgs/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `svgs/menu-${currentTheme}.svg`;
  }
}
