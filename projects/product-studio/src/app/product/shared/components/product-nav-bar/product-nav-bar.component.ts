
import { ChangeDetectorRef, Component, Input, OnInit, Output } from '@angular/core';
import {
  ButtonComponent,
  HeaderComponent,
  HeadingComponent,
} from '@awe/play-comp-library';
import { CommonModule } from '@angular/common';
import { UserProfile } from '../../models/user-profile.model';
import { ThemeServiceService } from '../../services/auth-config-service/theme-service.service';
import { AppConstants } from '../../appConstants';
import { AuthService, TokenStorageService } from '@shared/index';
import { ToastService } from 'projects/experience-studio/src/app/shared/services/toast.service';
import { Router } from '@angular/router';
import { createLogger } from 'projects/experience-studio/src/app/shared/utils/logger';
// import { createLogger } from 'projects/experience-studio/src/app/shared/utils';
// import { ThemeServiceService } from '../../services/theme-service.service';

@Component({
  selector: 'app-product-nav-bar',
  standalone: true,
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  templateUrl: './product-nav-bar.component.html',
  styleUrl: './product-nav-bar.component.scss',
})
export class ProductNavBarComponent implements OnInit {
  // @Output() logout = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';
private logger = createLogger('NavHeaderComponent');
  constructor(
    private themeService: ThemeServiceService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private toastService: ToastService,
  ) {}

  ngOnInit(): void {
    this.updateThemeAssets();
    new MutationObserver(() => this.updateThemeAssets()).observe(
      document.body,
      {
        attributes: true,
        attributeFilter: ['class'],
      },
    );
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }

  onLogout(): void {
    try {
      const currentUrl = window.location.origin;
      this.authService.logout(currentUrl).subscribe({
        next: () => {
          this.logger.debug('Logout successful');
        },
        error: (error) => {
          this.logger.error('Logout failed:', error);
          // Clear tokens locally even if logout API fails
          this.tokenStorageService.clearTokens();
          this.router.navigate(['/login']);
        },
      });
    } catch (error) {
      this.logger.error('Error during logout:', error);
      this.toastService.error('Logout failed');
    }
  }

  onLogin(): void {
    try {
      this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).subscribe({
        next: () => this.logger.debug('Login successful'),
        error: (error) => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      this.logger.error('Error during login:', error);
      this.toastService.error('Login failed');
    }
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `svgs/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.userProfile?.displayName || 'User Profile';
  }

  getEmail(): string {
    return (
      this.userProfile?.mail ||
      this.userProfile?.userPrincipalName ||
      '<EMAIL>'
    );
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `svgs/product-studio-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `svgs/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `svgs/menu-${currentTheme}.svg`;
  }
}
