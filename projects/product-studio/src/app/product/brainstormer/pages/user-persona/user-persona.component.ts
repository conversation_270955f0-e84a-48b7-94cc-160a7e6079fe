import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  PersonaDataService,
  PersonaData,
  PersonaCard,
} from '../../services/persona-data.service';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import {
  HeadingComponent,
  IconsComponent,
  InputComponent,
  SliderComponent,
} from '@awe/play-comp-library';

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweCardComponent,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
  ],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss'],
})
export class UserPersonaComponent implements OnInit, OnDestroy {
  @Output() personaSelected = new EventEmitter<string>();

  colonIcon: string = '/svgs/colon.svg';
  awe_delete: string = '/icons/awe-delete.svg';

  currentPage: number = 1;
  itemsPerPage: number = 5; // Display 4 cards per page

  isModalOpen = false;
  modalMode: 'add' | 'edit' = 'add';
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  regeneratePrompt: string = '';

  personas: PersonaData[] = [];
  private subscription = new Subscription();

  isDeleteModalOpen = false;
  personaToDelete: PersonaData | null = null;

  constructor(private personaDataService: PersonaDataService) {}

  ngOnInit(): void {
    this.subscription.add(
      this.personaDataService.personas$.subscribe((personas) => {
        this.personas = personas;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  get currentPagePersonas(): PersonaData[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    return this.personas.slice(startIndex, startIndex + this.itemsPerPage);
  }

  get totalPages(): number {
    return Math.ceil(this.personas.length / this.itemsPerPage);
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  openAddModal(): void {
    this.modalMode = 'add';
    this.selectedCardForEdit = {
      id: 'new-persona-profile',
      title: 'Profile',
      type: 'profile',
      data: {},
    };
    this.setupEditDataForAdd();
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit) return;

    if (this.modalMode === 'add') {
      const personalityArray = Array.isArray(this.editData.personality)
        ? this.editData.personality
        : [];

      const newPersonaData =
        this.personaDataService.convertUserPersonaToPersonaData({
          ...this.editData,
          personality: personalityArray,
          avatar: this.personaDataService.getDefaultAvatar(
            this.editData.role || '',
          ),
        });
      this.personaDataService.addPersona(newPersonaData);
      this.currentPage = this.totalPages;
    }
    this.closeModal();
  }

  openDeleteModal(persona: PersonaData): void {
    this.isDeleteModalOpen = true;
    this.personaToDelete = persona;
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
    this.personaToDelete = null;
  }

  confirmDeletePersona(): void {
    if (this.personaToDelete) {
      this.personaDataService.deletePersona(this.personaToDelete.id);
      if (this.currentPage > this.totalPages) {
        this.currentPage = this.totalPages;
      }
    }
    this.closeDeleteModal();
  }

  onPersonaClick(persona: PersonaData): void {
    this.personaDataService.setSelectedPersona(persona.id);
    this.personaSelected.emit(persona.id);
  }

  trackByPersona(_index: number, persona: PersonaData): string {
    return persona.id;
  }

  // Modal Helper Methods
  private setupEditDataForAdd(): void {
    this.editData = {
      name: '',
      role: '',
      age: '',
      education: '',
      status: '',
      location: '',
      techLiteracy: 'Medium',
      quote: '',
      avatar: '',
      personality: [],
    };
  }

  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  trackByIndex(index: number): number {
    return index;
  }

  addArrayItem(): void {
    if (!this.selectedCardForEdit) return;

    if (this.selectedCardForEdit.type === 'skills') {
      if (!Array.isArray(this.editData)) this.editData = [];
      this.editData.push({ name: '', level: 50 });
    } else if (Array.isArray(this.editData)) {
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    if (
      Array.isArray(this.editData) &&
      index >= 0 &&
      index < this.editData.length
    ) {
      this.editData.splice(index, 1);
    }
  }
}
