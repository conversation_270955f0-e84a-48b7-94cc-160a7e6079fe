// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap");
// Section Headers
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  background: var(--feature-card-header-bg) !important;
  border-radius: 12px 12px 0px 0px;
  color: var(--feature-card-header-title-color);

  .section-title {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 1px;
  }
  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--feature-card-header-action-bg);
    border-radius: 16px;
    color: var(--feature-card-header-action-color);
    padding: 16px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
  }
}
.feature-item-card {
  --awe-card-border: #e9ecef;
  --awe-card-box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
  --awe-card-border-radius: 0.5rem;
  --awe-card-padding-base: 1rem;
  display: flex;
  flex-direction: column;
  font-family: "Mulish", sans-serif;
  font-weight: 600;
  color: var(--feature-card-color);
  font-size: 1rem;
  border-radius: var(--feature-card-border-redius);
  border: 1px solid var(--awe-card-border);
  background: var(--feature-card-bg);
  box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.1);
  cursor: grab !important;

  .feature-description {
    color: var(--feature-card-desc-color);
    // font-size: 1rem;
    line-height: 1.5;
  }
  &:hover {
    --awe-card-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

:host ::ng-deep .feature-title {
  color: var(--feature-card-title);
}

// Custom dropdown styling
.dropdown-arrow {
  position: relative;
  height: 44px;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 35%;
    right: 70%;
    min-width: 120px;
    background-color: #fff;
    // border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    // padding: 0.5rem 0;
    z-index: 1000;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      // border-bottom: 1px solid #303233;
      // padding: 0.375rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      // border: 0;
      transition: background-color 0.15s ease-in-out;
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }
    }

    button {
      min-width: 0px;
      border: none;
      background: none;
    }
  }
}

.inp-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  // align-items: stretch;
  gap: 10px;
  margin-bottom: 0;
  .label {
    width: auto;
    display: flex;
    align-self: center;
    align-items: center;
    font-size: 1rem;
    flex-grow: 0;
  }
  .input-wrapper {
    width: auto;
    flex-grow: 1;
  }
}

.border-buttom {
  border-bottom: 1px solid #303233;
}
.three-dot-icon {
  margin: 1.25rem;
  cursor: pointer !important;
}

:host ::ng-deep awe-body-text {
}

.add-more-section {
  .add-more-btn {
    background-color: var(--feature-cared-add-card-btn-bg);
    border: 1px solid var(--feature-cared-add-card-btn-border);
    border-radius: 12px;
    color: var(--feature-cared-add-card-btn-color);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;

    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba(108, 117, 125, 0.1);
  border: 2px dashed #6c757d;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.feature-list-dropzone.cdk-drop-list-dragging
  awe-card.feature-item-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}

.edit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.feature-tag {
  background-color: var(--feature-tag-bg);
  color: var(--feature-tag-color);
  border-radius: 2rem;
  gap: 1rem;
  line-height: 2rem;
}

.feature-list-dropzone {
  cursor: grab !important;
  &:active {
    cursor: grabbing !important;
  }
}

.feature-card-main {
  background-color: var(--feature-card-bg);
  min-height: 655px;
}

// STYLES FOR THE MODAL CONTENT
.edit-modal-header {
  .modal-title {
    font-weight: 600;
  }
}

:host ::ng-deep .input-container label {
  display: none;
}
:host ::ng-deep .input-container .input-wrapper.expanded {
  height: 125px;
}
:host ::ng-deep .input-container {
  padding: 0;
}
.add-new {
  border: 2px solid #858aad;
  border-radius: 0.5rem;
  height: 60px;
  background: none;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
  &:hover {
    background-color: #e9ecef;
  }
}

button {
  border-radius: 50px;
  background: #fff;
  padding: 12px 24px;
  border: none;
  border-radius: 50px;
}
.btn-cancel {
  border: 1px solid var(--Primary-500, #7c3aed);
  background: var(--Neutral-Neutral-colors-Solid, #fff);
}
.btn-delete {
  background: var(--Primary-500, #7c3aed);
  border: 1px solid var(--Primary-500, #7c3aed);
  color: #fff;
}
