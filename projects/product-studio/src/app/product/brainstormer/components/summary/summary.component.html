<div class="project-summary-container p-4 p-md-5" *ngIf="projectData">
  <!-- TOP SECTION: Title, Description, and Progress -->
  <div class="row mb-5">
    <div class="col-12 col-lg-12 col-md-12">
      <h1 class="project-title">{{ projectData.name }}</h1>
      <p class="project-description">{{ projectData.description }}</p>
    </div>
  </div>
  <div class="row gap-3">
    <div
      class="col-12 col-lg-4 d-flex align-items-center justify-content-lg-end mt-4 mt-lg-0"
    >
      <div class="progress-wrapper d-flex align-items-center">
        <awe-progress-bar
          [progress]="projectData.progress"
          class="my-custom-bar"
        ></awe-progress-bar>

        <!-- <div class="progress-circle" [style.--progress]="projectData.progress">
          <span class="progress-text">{{ projectData.progress }}%</span>
        </div> -->
        <div class="ms-4">
          <h4 class="progress-heading">Phew! That was a productive session</h4>
          <p
            class="progress-subheading"
            [innerHTML]="projectData.contributionText"
          ></p>
          <button class="btn btn-primary export-btn">Export</button>
        </div>
      </div>
    </div>
  </div>

  <!-- MAIN GRID OF CARDS -->
  <div class="row g-4 mt-4">
    <!-- Left Column -->
    <div class="col-12 col-lg-8 d-flex flex-column gap-4">
      <div class="row g-4">
        <!-- Understanding -->
        <div class="col-12 col-md-6 d-flex">
          <awe-card [applyBodyPadding]="true" [showFooter]="true">
            <div awe-card-header-content class="px-4 pt-4">
              <awe-heading variant="s1" type="bold">Understanding</awe-heading>
            </div>
            <div class="px-4">
              <awe-body-text class="px-4">
                {{ projectData.understandingText }}
              </awe-body-text>
            </div>
            <div awe-card-footer-content class="d-flex justify-content-end">
              <button class="btn-icon">
                <awe-icons
                  iconName="awe_arrow_circle_right_outlined"
                ></awe-icons>
              </button>
            </div>
          </awe-card>
        </div>

        <!-- UserPersona -->
        <div class="col-12 col-md-6 d-flex">
          <awe-card [applyBodyPadding]="true" [showFooter]="true">
            <div awe-card-header-content class="px-4 pt-3">
              <awe-heading variant="s1" type="bold">User Persona</awe-heading>
            </div>
            <div class="persona-list row g-3 px-4 pt-4">
              <div
                *ngFor="let persona of projectData.personas"
                class="persona-item col-5 col-lg-5 col-md-5 d-flex align-items-center justify-content-start"
              >
                <img
                  [src]="persona.avatarUrl"
                  [alt]="persona.name"
                  class="avatar p-2"
                />
                <div>
                  <awe-caption inputId="caption" ariaLabel="caption">{{
                    persona.name
                  }}</awe-caption>
                  <awe-caption
                    inputId="caption"
                    class="persona-role"
                    ariaLabel="caption"
                    >{{ persona.role }}</awe-caption
                  >

                  <!-- <div class="persona-name px-2"></div>
                  <div >{{ persona.role }}</div> -->
                </div>
              </div>
            </div>
            <div awe-card-footer-content class="d-flex justify-content-end">
              <button class="btn-icon">
                <awe-icons
                  iconName="awe_arrow_circle_right_outlined"
                ></awe-icons>
              </button>
            </div>
          </awe-card>
        </div>
      </div>

      <div class="row g-4">
        <!-- Feature List -->
        <div class="col-12 col-md-6 d-flex">
          <awe-card [applyBodyPadding]="true" [showFooter]="true">
            <div awe-card-header-content class="px-4 pt-4">
              <awe-heading variant="s1" type="bold">Feature List</awe-heading>
            </div>
            <div class="feature-list row px-4 pt-4">
              <div
                class="task-bar col-6 col-lg-3 col-md-3 col-sm-6 py-2 px-2"
                *ngFor="let feature of projectData.features"
                [style.border-top]="`3px solid ${feature.color}`"
              >
                <awe-caption
                  *ngIf="feature"
                  inputId="caption"
                  ariaLabel="caption"
                  >{{ feature.name }}</awe-caption
                >
              </div>
            </div>
            <div awe-card-footer-content class="d-flex justify-content-end">
              <button class="btn-icon">
                <awe-icons
                  iconName="awe_arrow_circle_right_outlined"
                ></awe-icons>
              </button>
            </div>
          </awe-card>
        </div>

        <!-- SWOT Analysis -->
        <div class="col-12 col-md-6 d-flex">
          <awe-card [applyBodyPadding]="true" [showFooter]="true">
            <div awe-card-header-content class="px-4 pt-4">
              <awe-heading variant="s1" type="bold"> SWOT Analysis</awe-heading>
            </div>
            <div class="feature-list row px-4 pt-4">
              <div
                class="task-bar-swot py-2 px-4 d-flex justify-content-start align-items-center"
                *ngFor="let item of projectData.swot"
                [style.border-left]="`7px solid ${item.color}`"
              >
                <ul
                  *ngIf="item.category"
                  class="p-0 m-0"
                  variant="s2"
                  type="bold"
                >
                  <li *ngFor="let point of item.points" class="p-0 m-0">
                    {{ point }}
                  </li>
                </ul>
              </div>
            </div>
            <div awe-card-footer-content class="d-flex justify-content-end">
              <button class="btn-icon">
                <awe-icons
                  iconName="awe_arrow_circle_right_outlined"
                ></awe-icons>
              </button>
            </div>
          </awe-card>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="col-12 col-lg-4 d-flex flex-column gap-4">
      <div class="col-12 d-flex">
        <awe-card [applyBodyPadding]="false" [showFooter]="true">
          <!-- Card Header -->
          <div awe-card-header-content class="p-4">
            <h4>ROADMAP</h4>
          </div>

          <!-- Card Body with Timeline -->
          <div class="p-4 pt-0">
            <div class="timeline-container">
              <!-- The main vertical line running down the center -->
              <div class="timeline-main-line"></div>

              <!-- Loop through each quarter -->
              <div
                *ngFor="let quarter of projectData.timeline; let isLast = last"
                class="timeline-item"
              >
                <div class="timeline-content">
                  <h5 class="quarter-title">{{ quarter.quarter }}</h5>
                  <div class="quarter-tasks">
                    <div *ngFor="let task of quarter.items" class="task-item">
                      <awe-icons
                        [iconName]="task.icon"
                        iconSize="16px"
                      ></awe-icons>
                      <span>{{ task.label }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Card Footer -->
          <div awe-card-footer-content class="d-flex justify-content-end">
            <button class="btn-icon">
              <awe-icons iconName="awe_arrow_circle_right_outlined"></awe-icons>
            </button>
          </div>
        </awe-card>
      </div>
    </div>
  </div>
</div>
