import { ApplicationConfig, importProv<PERSON>sFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { routes } from './app.routes';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import { LucideAngularModule, Hammer, User, Settings, Info, ChevronLeft, ChevronRight, ShieldAlert, Hourglass, CircleCheck, XCircle, AlignVerticalDistributeStart, CircleCheckBig, MoveLeft, Play, CalendarDays, EllipsisVertical, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Edit, Trash, Plus, Minus, ChevronDown, ChevronUp, Eye, Home, Layout, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip, Bot, Archive, Copy, Trash2, Users, Wrench, TrendingUp, PanelLeft, BookOpen, NotebookText, Redo, RotateCcw, Swords, Undo, Pencil, RotateCw, SendHorizontal, WandSparkles, MousePointer2, Hand, ZoomIn, ZoomOut, Clock, CircleX, FileText, Download, Save } from 'lucide-angular';
import { MarkdownModule } from 'ngx-markdown';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
       importProvidersFrom(
          LucideAngularModule.pick({
            Hammer,
            User,
            Settings,
            Info,
            ChevronLeft,
            ChevronRight,
            ShieldAlert,
            Hourglass,
            CircleCheck,
            XCircle,
            AlignVerticalDistributeStart,
            CircleCheckBig,
            MoveLeft,
            Play,
            CalendarDays,
            EllipsisVertical,
            SquarePen,
            Wifi,
            Search,
            AlertCircle,
            EyeOff,
            Mail,
            Phone,
            Check,
            X,
            Edit,
            Trash,
            Plus,
            Minus,
            ChevronDown,
            ChevronUp,
            Eye,
            Home,
            Layout,
            Bell,
            Grid,
            Star,
            Leaf,
            CheckCircle,
            AlertTriangle,
            XOctagon,
            Sparkles,
            Slash,
            Feather,
            Globe,
            Send,
            Box,
            Paperclip,
            Bot,
            Archive,
            Copy,
            Trash2,
            Users,
            Wrench,
            TrendingUp,
            PanelLeft,
            BookOpen,
            NotebookText,
            Redo,
            RotateCcw,
            Swords,
            Undo,
            Pencil,
            RotateCw,
            SendHorizontal,
            WandSparkles,
            MousePointer2,
            Hand,
            ZoomIn,
            ZoomOut,
            Clock,
            CircleX,
            FileText,
            Download,
            Save
          }),
          MarkdownModule.forRoot(),
        ),
  ],
};
