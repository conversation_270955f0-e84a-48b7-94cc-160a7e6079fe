import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { environment } from '../environments/environment';
import { ProductNavBarComponent } from "./product/shared/components/product-nav-bar/product-nav-bar.component";

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    ProductNavBarComponent
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.productStudioApiAuthUrl,
      redirectUrl: environment.productStudioRedirectUrl,
      postLoginRedirectUrl: '/dashboard',
      appName: 'product-studio',
    };
    this.authService.setAuthConfig(authConfig);
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    
    if (!this.tokenStorage.getCookie('org_path')) {
      this.tokenStorage.setCookie(
        'org_path',
        'ascendion@core@genai@aava::201@202@203@204',
      );
    }
    
    this.router.events.subscribe((event) => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
      } else {
        this.showHeaderAndNav = true;
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }
}
